#!/usr/bin/env python3
"""
快速测试 --paper-trading 参数修复
"""

import argparse

def test_args():
    """测试参数解析"""
    print("🧪 测试 --paper-trading 参数...")
    
    # 创建解析器（与 run_complete_system.py 相同）
    parser = argparse.ArgumentParser(description="Binance-Lighter套利交易系统")
    parser.add_argument(
        "--config",
        default="config/settings.yaml",
        help="配置文件路径 (默认: config/settings.yaml)"
    )
    parser.add_argument(
        "--test",
        action="store_true",
        help="运行测试模式"
    )
    parser.add_argument(
        "--paper-trading",
        action="store_true",
        help="启用模拟交易模式（推荐用于测试）"
    )
    parser.add_argument(
        "--enable-trading",
        action="store_true",
        help="启用实际交易（谨慎使用）"
    )
    
    # 测试不同的参数组合
    test_cases = [
        ["--paper-trading"],
        ["--enable-trading"],
        ["--config", "config/production.yaml", "--paper-trading"],
        []  # 默认参数
    ]
    
    for i, args in enumerate(test_cases):
        try:
            parsed = parser.parse_args(args)
            print(f"✅ 测试 {i+1}: {args if args else '默认参数'}")
            print(f"   paper_trading: {parsed.paper_trading}")
            print(f"   enable_trading: {parsed.enable_trading}")
            print(f"   config: {parsed.config}")
            print()
        except Exception as e:
            print(f"❌ 测试 {i+1} 失败: {e}")
            return False
    
    return True

if __name__ == "__main__":
    print("🚀 快速测试 --paper-trading 参数修复")
    print("=" * 50)
    
    if test_args():
        print("🎉 参数解析测试通过！")
        print("\n📝 修复成功！现在您可以使用:")
        print("   python3 run_complete_system.py --paper-trading")
        print("   python3 run_complete_system.py --enable-trading")
        print("   python3 run_complete_system.py --config config/production.yaml --paper-trading")
    else:
        print("❌ 测试失败")
