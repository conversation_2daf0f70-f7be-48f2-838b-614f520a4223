#!/usr/bin/env python3
"""
测试 --paper-trading 参数修复
"""

import sys
import argparse
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_argument_parsing():
    """测试参数解析"""
    print("🧪 测试参数解析...")
    
    # 模拟命令行参数
    test_args = [
        ["--paper-trading"],
        ["--enable-trading"],
        ["--config", "config/settings.yaml", "--paper-trading"],
        ["--help"]
    ]
    
    for args in test_args[:-1]:  # 跳过 --help 因为它会退出程序
        try:
            # 创建解析器（复制自 run_complete_system.py）
            parser = argparse.ArgumentParser(description="Binance-Lighter套利交易系统")
            parser.add_argument(
                "--config",
                default="config/settings.yaml",
                help="配置文件路径 (默认: config/settings.yaml)"
            )
            parser.add_argument(
                "--test",
                action="store_true",
                help="运行测试模式"
            )
            parser.add_argument(
                "--paper-trading",
                action="store_true",
                help="启用模拟交易模式（推荐用于测试）"
            )
            parser.add_argument(
                "--enable-trading",
                action="store_true",
                help="启用实际交易（谨慎使用）"
            )
            
            # 解析参数
            parsed_args = parser.parse_args(args)
            
            print(f"✅ 参数 {args} 解析成功:")
            print(f"   paper_trading: {parsed_args.paper_trading}")
            print(f"   enable_trading: {parsed_args.enable_trading}")
            print(f"   config: {parsed_args.config}")
            
        except Exception as e:
            print(f"❌ 参数 {args} 解析失败: {e}")
            return False
    
    return True

def test_system_import():
    """测试系统导入"""
    print("\n🧪 测试系统导入...")
    
    try:
        from run_complete_system import ArbitrageTradingSystem
        print("✅ ArbitrageTradingSystem 导入成功")
        
        # 测试创建实例
        system = ArbitrageTradingSystem(
            config_path="config/settings.yaml",
            paper_trading=True,
            enable_trading=False
        )
        print("✅ ArbitrageTradingSystem 实例创建成功")
        print(f"   paper_trading: {system.paper_trading}")
        print(f"   enable_trading: {system.enable_trading}")
        
        return True
        
    except Exception as e:
        print(f"❌ 系统导入失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 开始测试 --paper-trading 参数修复")
    print("=" * 50)
    
    # 测试参数解析
    test1_passed = test_argument_parsing()
    
    # 测试系统导入
    test2_passed = test_system_import()
    
    # 总结
    print("\n" + "=" * 50)
    print("📊 测试结果:")
    print(f"   参数解析: {'✅ 通过' if test1_passed else '❌ 失败'}")
    print(f"   系统导入: {'✅ 通过' if test2_passed else '❌ 失败'}")
    
    if test1_passed and test2_passed:
        print("\n🎉 所有测试通过！--paper-trading 参数修复成功！")
        print("\n📝 现在您可以使用以下命令:")
        print("   python3 run_complete_system.py --paper-trading")
        print("   python3 run_complete_system.py --enable-trading")
        print("   python3 run_complete_system.py --config config/production.yaml --paper-trading")
        return True
    else:
        print("\n❌ 部分测试失败，需要进一步检查")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
