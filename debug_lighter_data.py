#!/usr/bin/env python3
"""
调试Lighter数据格式的简单脚本
"""

import asyncio
import time
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.exchanges.lighter_client import LighterClient
from src.utils.logger import setup_logging
import structlog

# 设置日志 - 显示DEBUG级别
setup_logging(level='DEBUG', console_logging=True, colored_logs=True)
logger = structlog.get_logger(__name__)


async def debug_lighter_data():
    """调试Lighter数据格式"""
    logger.info("🔍 开始调试Lighter数据格式...")
    
    # 创建Lighter客户端
    client = LighterClient(symbol="BTC/USDT", is_paper_trading=True)
    
    try:
        # 初始化客户端
        logger.info("🔌 初始化Lighter客户端...")
        success = await client.initialize()
        
        if not success:
            logger.error("❌ Lighter客户端初始化失败")
            return
        
        logger.info("✅ Lighter客户端初始化成功")
        
        # 等待30秒收集数据
        logger.info("📊 等待30秒收集数据...")
        await asyncio.sleep(30)
        
        # 检查状态
        status = client.get_status()
        logger.info("📈 客户端状态", **status)
        
        # 尝试获取订单簿
        try:
            orderbook = client.get_orderbook("BTC/USDT")
            logger.info("📖 订单簿数据", orderbook=orderbook)
        except Exception as e:
            logger.error("❌ 获取订单簿失败", error=str(e))
        
        # 尝试获取价格
        try:
            price = client.get_current_price("BTC/USDT")
            logger.info("💰 当前价格", price=price)
        except Exception as e:
            logger.error("❌ 获取价格失败", error=str(e))
        
    except Exception as e:
        logger.error("❌ 调试过程中发生异常", error=str(e))
        
    finally:
        # 清理资源
        logger.info("🧹 清理资源...")
        await client.close()
        logger.info("✅ 调试完成")


if __name__ == "__main__":
    asyncio.run(debug_lighter_data())
