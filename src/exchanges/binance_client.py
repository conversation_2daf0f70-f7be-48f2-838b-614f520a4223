"""
Binance交易所客户端

使用CCXT Pro提供WebSocket实时数据和交易功能
"""

import ccxt.async_support as ccxt
import ccxt.pro as ccxtpro
import asyncio
import time
from typing import Dict, List, Optional, Callable, Any, Tuple
from decimal import Decimal
import structlog
from dataclasses import dataclass

logger = structlog.get_logger(__name__)


@dataclass
class OrderBook:
    """订单簿数据"""
    symbol: str
    bids: List[List[float]]  # [[price, amount], ...]
    asks: List[List[float]]  # [[price, amount], ...]
    timestamp: float


@dataclass
class Trade:
    """交易数据"""
    symbol: str
    price: float
    amount: float
    side: str  # 'buy' or 'sell'
    timestamp: float


@dataclass
class Order:
    """订单数据"""
    id: str
    symbol: str
    side: str
    amount: float
    price: float
    status: str
    filled: float
    remaining: float
    timestamp: float


class BinanceClient:
    """Binance交易所客户端"""
    
    def __init__(self, api_key: str, secret: str, sandbox: bool = True, testnet: bool = True, is_paper_trading: bool = False):
        """
        初始化Binance客户端
        
        Args:
            api_key: API密钥
            secret: API秘钥
            sandbox: 是否使用沙盒环境
            testnet: 是否使用测试网
            is_paper_trading: 是否为模拟交易模式
        """
        self.api_key = api_key
        self.secret = secret
        self.sandbox = sandbox
        self.testnet = testnet
        self.is_paper_trading = is_paper_trading
        
        # 配置选项
        options = {
            'enableRateLimit': True,
            'newUpdates': False,  # 使用缓存模式，获取完整数据
        }
        
        # 模拟交易模式下不使用API密钥进行私有操作
        if not is_paper_trading:
            options.update({
                'apiKey': api_key,
                'secret': secret,
                'sandbox': sandbox,
                'test': testnet
            })
        
        # 初始化CCXT Pro实例（用于WebSocket）
        self.exchange_pro = ccxtpro.binance(options)
        
        # 初始化CCXT实例（用于REST API，如果需要）
        if not is_paper_trading:
            self.exchange = ccxt.binance(options)
        else:
            self.exchange = None
            logger.info("模拟交易模式 - 跳过Binance私有API初始化")
        
        # 回调函数
        self.orderbook_callback: Optional[Callable] = None
        self.trade_callback: Optional[Callable] = None
        
        # 数据缓存
        self.orderbooks = {}
        self.last_trades = {}
        
        # 连接状态
        self.is_connected = False
        
        # WebSocket任务
        self._watch_tasks = []
        
    async def initialize(self) -> None:
        """初始化客户端"""
        try:
            self.is_connected = True
            logger.info("Binance客户端初始化成功 (CCXT Pro)", 
                       testnet=self.testnet, 
                       paper_trading=self.is_paper_trading)
            
        except Exception as e:
            logger.error("Binance客户端初始化失败", error=str(e))
            raise
    
    async def get_orderbook(self, symbol: str, limit: int = 20) -> OrderBook:
        """
        获取订单簿
        
        Args:
            symbol: 交易对符号
            limit: 深度限制
            
        Returns:
            订单簿数据
        """
        try:
            if self.exchange:
                orderbook = await self.exchange.fetch_order_book(symbol, limit)
            else:
                # 模拟交易模式下，使用CCXT Pro获取公开数据
                orderbook = await self.exchange_pro.fetch_order_book(symbol, limit)
            
            return OrderBook(
                symbol=symbol,
                bids=orderbook['bids'],
                asks=orderbook['asks'],
                timestamp=orderbook['timestamp']
            )
            
        except Exception as e:
            logger.error("获取订单簿失败", symbol=symbol, error=str(e))
            raise
    
    async def get_ticker(self, symbol: str) -> Dict[str, Any]:
        """
        获取ticker数据
        
        Args:
            symbol: 交易对符号
            
        Returns:
            ticker数据
        """
        try:
            if self.exchange:
                ticker = await self.exchange.fetch_ticker(symbol)
            else:
                # 模拟交易模式下，使用CCXT Pro获取公开数据
                ticker = await self.exchange_pro.fetch_ticker(symbol)
            return ticker
        except Exception as e:
            logger.error("获取ticker失败", symbol=symbol, error=str(e))
            raise
    
    async def place_order(self, symbol: str, side: str, amount: float, 
                         price: Optional[float] = None, order_type: str = 'limit') -> Order:
        """
        下单
        
        Args:
            symbol: 交易对符号
            side: 买卖方向 ('buy' or 'sell')
            amount: 数量
            price: 价格（限价单必填）
            order_type: 订单类型 ('market' or 'limit')
            
        Returns:
            订单信息
        """
        try:
            if self.is_paper_trading:
                # 模拟交易
                order_id = f'paper_binance_{int(time.time() * 1000)}'
                order = {
                    'id': order_id,
                    'status': 'filled',
                    'filled': amount,
                    'remaining': 0,
                    'timestamp': time.time() * 1000
                }
                logger.info("模拟交易：Binance订单", 
                           order_id=order_id, 
                           symbol=symbol,
                           side=side, 
                           amount=amount, 
                           price=price)
            else:
                if order_type == 'limit' and price is None:
                    raise ValueError("限价单必须指定价格")
                
                order = await self.exchange.create_order(
                    symbol=symbol,
                    type=order_type,
                    side=side,
                    amount=amount,
                    price=price
                )
                
                logger.info("订单下单成功",
                           order_id=order['id'],
                           symbol=symbol,
                           side=side,
                           amount=amount,
                           price=price,
                           type=order_type)
            
            return Order(
                id=order['id'],
                symbol=symbol,
                side=side,
                amount=amount,
                price=price or 0,
                status=order['status'],
                filled=order.get('filled', 0),
                remaining=order.get('remaining', amount),
                timestamp=order['timestamp']
            )
            
        except Exception as e:
            logger.error("下单失败", symbol=symbol, side=side, amount=amount, price=price, error=str(e))
            raise
    
    async def cancel_order(self, order_id: str, symbol: str) -> bool:
        """
        取消订单
        
        Args:
            order_id: 订单ID
            symbol: 交易对符号
            
        Returns:
            是否取消成功
        """
        try:
            if self.is_paper_trading:
                logger.info("模拟交易：取消订单", order_id=order_id, symbol=symbol)
                return True
            
            result = await self.exchange.cancel_order(order_id, symbol)
            logger.info("订单取消成功", order_id=order_id, symbol=symbol)
            return True
            
        except Exception as e:
            logger.error("订单取消失败", order_id=order_id, symbol=symbol, error=str(e))
            return False
    
    async def get_order_status(self, order_id: str, symbol: str) -> Optional[Order]:
        """
        获取订单状态
        
        Args:
            order_id: 订单ID
            symbol: 交易对符号
            
        Returns:
            订单信息
        """
        try:
            if self.is_paper_trading:
                # 模拟交易模式下返回模拟订单状态
                return Order(
                    id=order_id,
                    symbol=symbol,
                    side='buy',
                    amount=0.001,
                    price=50000.0,
                    status='filled',
                    filled=0.001,
                    remaining=0,
                    timestamp=time.time() * 1000
                )
            
            order = await self.exchange.fetch_order(order_id, symbol)
            
            return Order(
                id=order['id'],
                symbol=symbol,
                side=order['side'],
                amount=order['amount'],
                price=order['price'],
                status=order['status'],
                filled=order.get('filled', 0),
                remaining=order.get('remaining', 0),
                timestamp=order['timestamp']
            )
            
        except Exception as e:
            logger.error("获取订单状态失败", order_id=order_id, symbol=symbol, error=str(e))
            return None
    
    async def get_balance(self) -> Dict[str, Dict[str, float]]:
        """
        获取账户余额
        
        Returns:
            余额信息
        """
        try:
            if self.is_paper_trading:
                # 模拟交易模式下返回模拟余额
                return {
                    'BTC': {'free': 1.0, 'used': 0.0, 'total': 1.0},
                    'USDT': {'free': 50000.0, 'used': 0.0, 'total': 50000.0},
                    'free': {'BTC': 1.0, 'USDT': 50000.0},
                    'used': {'BTC': 0.0, 'USDT': 0.0},
                    'total': {'BTC': 1.0, 'USDT': 50000.0}
                }
            
            balance = await self.exchange.fetch_balance()
            return balance
        except Exception as e:
            logger.error("获取余额失败", error=str(e))
            raise
    
    def set_orderbook_callback(self, callback: Callable[[OrderBook], None]) -> None:
        """设置订单簿回调函数"""
        self.orderbook_callback = callback
    
    def set_trade_callback(self, callback: Callable[[Trade], None]) -> None:
        """设置交易回调函数"""
        self.trade_callback = callback
    
    async def subscribe_orderbook(self, symbol: str) -> None:
        """
        订阅订单簿WebSocket
        
        Args:
            symbol: 交易对符号
        """
        try:
            # 启动订单簿监控任务
            task = asyncio.create_task(self._watch_orderbook(symbol))
            self._watch_tasks.append(task)
            
            logger.info("订阅订单簿成功 (CCXT Pro)", symbol=symbol)
            
        except Exception as e:
            logger.error("订阅订单簿失败", symbol=symbol, error=str(e))
            raise
    
    async def subscribe_trade(self, symbol: str) -> None:
        """
        订阅交易WebSocket
        
        Args:
            symbol: 交易对符号
        """
        try:
            # 启动交易监控任务
            task = asyncio.create_task(self._watch_trades(symbol))
            self._watch_tasks.append(task)
            
            logger.info("订阅交易数据成功 (CCXT Pro)", symbol=symbol)
            
        except Exception as e:
            logger.error("订阅交易数据失败", symbol=symbol, error=str(e))
            raise
    
    async def _watch_orderbook(self, symbol: str) -> None:
        """监控订单簿更新"""
        while True:
            try:
                # 使用CCXT Pro的watchOrderBook方法
                orderbook = await self.exchange_pro.watch_order_book(symbol)
                
                # 转换为自定义格式
                custom_orderbook = OrderBook(
                    symbol=symbol,
                    bids=orderbook['bids'][:10],  # 只取前10档
                    asks=orderbook['asks'][:10],  # 只取前10档
                    timestamp=orderbook['timestamp']
                )
                
                # 缓存数据
                self.orderbooks[symbol] = custom_orderbook
                
                # 调用回调函数
                if self.orderbook_callback:
                    self.orderbook_callback(custom_orderbook)
                    
                logger.debug("订单簿更新成功 (CCXT Pro)", 
                           symbol=symbol,
                           bid_price=custom_orderbook.bids[0][0] if custom_orderbook.bids else None,
                           ask_price=custom_orderbook.asks[0][0] if custom_orderbook.asks else None)
                    
            except Exception as e:
                logger.error("监控订单簿失败", symbol=symbol, error=str(e))
                await asyncio.sleep(5)  # 重连等待
    
    async def _watch_trades(self, symbol: str) -> None:
        """监控交易更新"""
        while True:
            try:
                # 使用CCXT Pro的watchTrades方法
                trades = await self.exchange_pro.watch_trades(symbol)
                
                if trades:
                    # 获取最新的交易
                    latest_trade = trades[-1]
                    
                    # 转换为自定义格式
                    custom_trade = Trade(
                        symbol=symbol,
                        price=latest_trade['price'],
                        amount=latest_trade['amount'],
                        side=latest_trade['side'],
                        timestamp=latest_trade['timestamp']
                    )
                    
                    # 缓存最新交易
                    self.last_trades[symbol] = custom_trade
                    
                    # 调用回调函数
                    if self.trade_callback:
                        self.trade_callback(custom_trade)
                        
                    logger.debug("交易数据更新成功 (CCXT Pro)", 
                               symbol=symbol, 
                               price=custom_trade.price, 
                               amount=custom_trade.amount)
                    
            except Exception as e:
                logger.error("监控交易失败", symbol=symbol, error=str(e))
                await asyncio.sleep(5)  # 重连等待
    
    def get_latest_price(self, symbol: str) -> Optional[float]:
        """获取最新价格"""
        if symbol in self.last_trades:
            return self.last_trades[symbol].price
        elif symbol in self.orderbooks:
            orderbook = self.orderbooks[symbol]
            if orderbook.bids and orderbook.asks:
                return (orderbook.bids[0][0] + orderbook.asks[0][0]) / 2
        return None
    
    def get_best_bid_ask(self, symbol: str) -> Optional[Tuple[float, float]]:
        """获取最佳买卖价"""
        if symbol in self.orderbooks:
            orderbook = self.orderbooks[symbol]
            if orderbook.bids and orderbook.asks:
                return orderbook.bids[0][0], orderbook.asks[0][0]
        return None
    
    async def close(self) -> None:
        """关闭客户端"""
        try:
            # 取消所有WebSocket任务
            for task in self._watch_tasks:
                if not task.done():
                    task.cancel()
            
            # 关闭CCXT Pro实例
            if self.exchange_pro:
                await self.exchange_pro.close()
            
            # 关闭CCXT实例
            if self.exchange:
                await self.exchange.close()
                
            self.is_connected = False
            logger.info("Binance客户端已关闭 (CCXT Pro)")
        except Exception as e:
            logger.error("关闭Binance客户端失败", error=str(e)) 