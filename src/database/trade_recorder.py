"""
交易记录器

负责记录和跟踪交易执行过程
"""

import uuid
from datetime import datetime
from decimal import Decimal
from typing import Optional, Dict, Any, List
from dataclasses import replace
import structlog

from .models import (
    TradeRecord, TradeType, TradeStatus, OrderSide,
    PriceRecord, SpreadRecord, PerformanceMetrics
)
from .database import DatabaseManager

logger = structlog.get_logger(__name__)


class TradeRecorder:
    """交易记录器"""
    
    def __init__(self, db_manager: DatabaseManager):
        """
        初始化交易记录器
        
        Args:
            db_manager: 数据库管理器
        """
        self.db_manager = db_manager
        self.active_trades: Dict[str, TradeRecord] = {}
        
    async def create_trade_record(
        self,
        symbol: str,
        trade_type: TradeType,
        expected_profit: Optional[Decimal] = None,
        spread_at_entry: Optional[Decimal] = None,
        position_size: Optional[Decimal] = None
    ) -> TradeRecord:
        """
        创建新的交易记录
        
        Args:
            symbol: 交易对
            trade_type: 交易类型
            expected_profit: 预期利润
            spread_at_entry: 入场时的价差
            position_size: 仓位大小
            
        Returns:
            交易记录
        """
        trade_id = str(uuid.uuid4())
        
        trade_record = TradeRecord(
            id=trade_id,
            timestamp=datetime.utcnow(),
            symbol=symbol,
            trade_type=trade_type,
            expected_profit=expected_profit,
            spread_at_entry=spread_at_entry,
            position_size=position_size,
            status=TradeStatus.PENDING
        )
        
        # 添加到活跃交易
        self.active_trades[trade_id] = trade_record
        
        # 保存到数据库
        await self.db_manager.save_trade(trade_record)
        
        logger.info(
            "创建交易记录",
            trade_id=trade_id,
            symbol=symbol,
            trade_type=trade_type.value,
            expected_profit=float(expected_profit) if expected_profit else None
        )
        
        return trade_record
    
    async def update_binance_order(
        self,
        trade_id: str,
        order_id: str,
        side: OrderSide,
        price: Decimal,
        quantity: Decimal,
        status: TradeStatus,
        fee: Optional[Decimal] = None
    ) -> None:
        """
        更新Binance订单信息
        
        Args:
            trade_id: 交易ID
            order_id: 订单ID
            side: 订单方向
            price: 价格
            quantity: 数量
            status: 状态
            fee: 手续费
        """
        if trade_id not in self.active_trades:
            logger.warning("未找到交易记录", trade_id=trade_id)
            return
        
        trade = self.active_trades[trade_id]
        
        # 更新Binance订单信息
        updated_trade = replace(
            trade,
            binance_order_id=order_id,
            binance_side=side,
            binance_price=price,
            binance_quantity=quantity,
            binance_status=status,
            binance_fee=fee
        )
        
        self.active_trades[trade_id] = updated_trade
        await self.db_manager.save_trade(updated_trade)
        
        logger.info(
            "更新Binance订单",
            trade_id=trade_id,
            order_id=order_id,
            side=side.value,
            price=float(price),
            quantity=float(quantity),
            status=status.value
        )
    
    async def update_lighter_order(
        self,
        trade_id: str,
        order_id: str,
        side: OrderSide,
        price: Decimal,
        quantity: Decimal,
        status: TradeStatus,
        fee: Optional[Decimal] = None
    ) -> None:
        """
        更新Lighter订单信息
        
        Args:
            trade_id: 交易ID
            order_id: 订单ID
            side: 订单方向
            price: 价格
            quantity: 数量
            status: 状态
            fee: 手续费
        """
        if trade_id not in self.active_trades:
            logger.warning("未找到交易记录", trade_id=trade_id)
            return
        
        trade = self.active_trades[trade_id]
        
        # 更新Lighter订单信息
        updated_trade = replace(
            trade,
            lighter_order_id=order_id,
            lighter_side=side,
            lighter_price=price,
            lighter_quantity=quantity,
            lighter_status=status,
            lighter_fee=fee
        )
        
        self.active_trades[trade_id] = updated_trade
        await self.db_manager.save_trade(updated_trade)
        
        logger.info(
            "更新Lighter订单",
            trade_id=trade_id,
            order_id=order_id,
            side=side.value,
            price=float(price),
            quantity=float(quantity),
            status=status.value
        )
    
    async def complete_trade(
        self,
        trade_id: str,
        actual_profit: Decimal,
        spread_at_exit: Optional[Decimal] = None,
        execution_time_ms: Optional[int] = None,
        slippage: Optional[Decimal] = None
    ) -> None:
        """
        完成交易
        
        Args:
            trade_id: 交易ID
            actual_profit: 实际利润
            spread_at_exit: 出场时的价差
            execution_time_ms: 执行时间（毫秒）
            slippage: 滑点
        """
        if trade_id not in self.active_trades:
            logger.warning("未找到交易记录", trade_id=trade_id)
            return
        
        trade = self.active_trades[trade_id]
        
        # 计算利润率
        profit_rate = None
        if trade.expected_profit and trade.expected_profit != 0:
            profit_rate = actual_profit / trade.expected_profit
        
        # 更新交易记录
        updated_trade = replace(
            trade,
            actual_profit=actual_profit,
            profit_rate=profit_rate,
            spread_at_exit=spread_at_exit,
            execution_time_ms=execution_time_ms,
            slippage=slippage,
            status=TradeStatus.EXECUTED
        )
        
        self.active_trades[trade_id] = updated_trade
        await self.db_manager.save_trade(updated_trade)
        
        # 从活跃交易中移除
        del self.active_trades[trade_id]
        
        logger.info(
            "交易完成",
            trade_id=trade_id,
            actual_profit=float(actual_profit),
            profit_rate=float(profit_rate) if profit_rate else None,
            execution_time_ms=execution_time_ms
        )
    
    async def fail_trade(
        self,
        trade_id: str,
        error_message: str
    ) -> None:
        """
        标记交易失败
        
        Args:
            trade_id: 交易ID
            error_message: 错误信息
        """
        if trade_id not in self.active_trades:
            logger.warning("未找到交易记录", trade_id=trade_id)
            return
        
        trade = self.active_trades[trade_id]
        
        # 更新交易记录
        updated_trade = replace(
            trade,
            status=TradeStatus.FAILED,
            error_message=error_message
        )
        
        self.active_trades[trade_id] = updated_trade
        await self.db_manager.save_trade(updated_trade)
        
        # 从活跃交易中移除
        del self.active_trades[trade_id]
        
        logger.error(
            "交易失败",
            trade_id=trade_id,
            error_message=error_message
        )
    
    async def cancel_trade(
        self,
        trade_id: str,
        reason: str = "用户取消"
    ) -> None:
        """
        取消交易
        
        Args:
            trade_id: 交易ID
            reason: 取消原因
        """
        if trade_id not in self.active_trades:
            logger.warning("未找到交易记录", trade_id=trade_id)
            return
        
        trade = self.active_trades[trade_id]
        
        # 更新交易记录
        updated_trade = replace(
            trade,
            status=TradeStatus.CANCELLED,
            error_message=reason
        )
        
        self.active_trades[trade_id] = updated_trade
        await self.db_manager.save_trade(updated_trade)
        
        # 从活跃交易中移除
        del self.active_trades[trade_id]
        
        logger.info(
            "交易取消",
            trade_id=trade_id,
            reason=reason
        )
    
    async def get_active_trades(self) -> List[TradeRecord]:
        """获取活跃交易列表"""
        return list(self.active_trades.values())
    
    async def get_active_trade_count(self) -> int:
        """获取活跃交易数量"""
        return len(self.active_trades)
    
    async def calculate_daily_performance(self, date: datetime) -> PerformanceMetrics:
        """
        计算日度性能指标
        
        Args:
            date: 计算日期
            
        Returns:
            性能指标
        """
        try:
            # 获取当日交易记录
            trades = await self.db_manager.get_trades_by_date(date)
            
            if not trades:
                return PerformanceMetrics(
                    timestamp=datetime.utcnow(),
                    period="daily",
                    total_trades=0,
                    successful_trades=0,
                    failed_trades=0,
                    success_rate=0.0,
                    total_profit=Decimal('0'),
                    total_loss=Decimal('0'),
                    net_profit=Decimal('0'),
                    profit_factor=0.0,
                    sharpe_ratio=0.0,
                    max_drawdown=Decimal('0'),
                    calmar_ratio=0.0,
                    avg_trade_duration_seconds=0.0,
                    avg_profit_per_trade=Decimal('0'),
                    avg_execution_time_ms=0.0
                )
            
            # 统计交易数量
            total_trades = len(trades)
            successful_trades = sum(1 for t in trades if t.get('status') == 'executed')
            failed_trades = sum(1 for t in trades if t.get('status') == 'failed')
            success_rate = successful_trades / total_trades if total_trades > 0 else 0.0
            
            # 计算盈亏
            profits = []
            total_profit = Decimal('0')
            total_loss = Decimal('0')
            execution_times = []
            
            for trade in trades:
                actual_profit = trade.get('actual_profit')
                if actual_profit is not None:
                    profit = Decimal(str(actual_profit))
                    profits.append(profit)
                    
                    if profit > 0:
                        total_profit += profit
                    else:
                        total_loss += abs(profit)
                
                exec_time = trade.get('execution_time_ms')
                if exec_time is not None:
                    execution_times.append(exec_time)
            
            net_profit = total_profit - total_loss
            profit_factor = float(total_profit / total_loss) if total_loss > 0 else 0.0
            
            # 计算其他指标
            avg_profit_per_trade = net_profit / total_trades if total_trades > 0 else Decimal('0')
            avg_execution_time = sum(execution_times) / len(execution_times) if execution_times else 0.0
            
            # 计算最大回撤（简化版）
            cumulative_profit = Decimal('0')
            peak = Decimal('0')
            max_drawdown = Decimal('0')
            
            for profit in profits:
                cumulative_profit += profit
                if cumulative_profit > peak:
                    peak = cumulative_profit
                drawdown = peak - cumulative_profit
                if drawdown > max_drawdown:
                    max_drawdown = drawdown
            
            # 计算夏普比率（简化版）
            if profits:
                import numpy as np
                profit_array = np.array([float(p) for p in profits])
                sharpe_ratio = np.mean(profit_array) / np.std(profit_array) if np.std(profit_array) > 0 else 0.0
            else:
                sharpe_ratio = 0.0
            
            # 计算卡尔玛比率
            calmar_ratio = float(net_profit / max_drawdown) if max_drawdown > 0 else 0.0
            
            return PerformanceMetrics(
                timestamp=datetime.utcnow(),
                period="daily",
                total_trades=total_trades,
                successful_trades=successful_trades,
                failed_trades=failed_trades,
                success_rate=success_rate,
                total_profit=total_profit,
                total_loss=total_loss,
                net_profit=net_profit,
                profit_factor=profit_factor,
                sharpe_ratio=sharpe_ratio,
                max_drawdown=max_drawdown,
                calmar_ratio=calmar_ratio,
                avg_trade_duration_seconds=0.0,  # 需要更多数据计算
                avg_profit_per_trade=avg_profit_per_trade,
                avg_execution_time_ms=avg_execution_time
            )
            
        except Exception as e:
            logger.error("计算日度性能指标失败", error=str(e), date=date.isoformat())
            raise
    
    async def record_price_data(
        self,
        symbol: str,
        exchange: str,
        bid_price: Decimal,
        ask_price: Decimal,
        last_price: Decimal,
        volume_24h: Optional[Decimal] = None,
        bid_quantity: Optional[Decimal] = None,
        ask_quantity: Optional[Decimal] = None
    ) -> None:
        """
        记录价格数据
        
        Args:
            symbol: 交易对
            exchange: 交易所
            bid_price: 买一价
            ask_price: 卖一价
            last_price: 最新成交价
            volume_24h: 24小时交易量
            bid_quantity: 买一量
            ask_quantity: 卖一量
        """
        price_record = PriceRecord(
            timestamp=datetime.utcnow(),
            symbol=symbol,
            exchange=exchange,
            bid_price=bid_price,
            ask_price=ask_price,
            last_price=last_price,
            volume_24h=volume_24h,
            bid_quantity=bid_quantity,
            ask_quantity=ask_quantity
        )
        
        await self.db_manager.save_price(price_record)
    
    async def record_spread_data(
        self,
        symbol: str,
        binance_bid: Decimal,
        binance_ask: Decimal,
        lighter_bid: Decimal,
        lighter_ask: Decimal,
        ma_spread: Optional[Decimal] = None,
        signal: Optional[str] = None,
        signal_strength: Optional[float] = None
    ) -> None:
        """
        记录价差数据
        
        Args:
            symbol: 交易对
            binance_bid: Binance买一价
            binance_ask: Binance卖一价
            lighter_bid: Lighter买一价
            lighter_ask: Lighter卖一价
            ma_spread: 移动平均价差
            signal: 交易信号
            signal_strength: 信号强度
        """
        # 计算价差
        bid_spread = binance_bid - lighter_ask
        ask_spread = lighter_bid - binance_ask
        
        # 计算中位价差
        binance_mid = (binance_bid + binance_ask) / 2
        lighter_mid = (lighter_bid + lighter_ask) / 2
        mid_spread = (binance_mid - lighter_mid) / lighter_mid
        
        spread_record = SpreadRecord(
            timestamp=datetime.utcnow(),
            symbol=symbol,
            binance_bid=binance_bid,
            binance_ask=binance_ask,
            lighter_bid=lighter_bid,
            lighter_ask=lighter_ask,
            bid_spread=bid_spread,
            ask_spread=ask_spread,
            mid_spread=mid_spread,
            ma_spread=ma_spread,
            signal=signal,
            signal_strength=signal_strength
        )
        
        await self.db_manager.save_spread(spread_record) 