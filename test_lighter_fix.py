#!/usr/bin/env python3
"""
测试Lighter客户端修复后的连续数据更新功能

这个脚本用于验证Lighter客户端的价格数据是否能够持续更新，
以及新增的健康检查和重连机制是否正常工作。
"""

import asyncio
import time
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.exchanges.lighter_client import LighterClient
from src.utils.logger import setup_logging
import structlog

# 设置日志
setup_logging(level='INFO', console_logging=True, colored_logs=True)
logger = structlog.get_logger(__name__)


class LighterTestMonitor:
    """Lighter客户端测试监控器"""
    
    def __init__(self):
        self.update_count = 0
        self.last_update_time = None
        self.price_history = []
        self.start_time = time.time()
        
    def on_orderbook_update(self, orderbook):
        """订单簿更新回调"""
        self.update_count += 1
        self.last_update_time = time.time()
        
        if orderbook.bids and orderbook.asks:
            bid_price = orderbook.bids[0][0]
            ask_price = orderbook.asks[0][0]
            mid_price = (bid_price + ask_price) / 2
            
            self.price_history.append({
                'timestamp': self.last_update_time,
                'bid': bid_price,
                'ask': ask_price,
                'mid': mid_price
            })
            
            # 只保留最近100个价格记录
            if len(self.price_history) > 100:
                self.price_history = self.price_history[-100:]
            
            elapsed = self.last_update_time - self.start_time
            logger.info("📊 Lighter价格更新", 
                       update_count=self.update_count,
                       elapsed_time=f"{elapsed:.1f}s",
                       bid=bid_price,
                       ask=ask_price,
                       mid=mid_price,
                       symbol=orderbook.symbol)
    
    def get_stats(self):
        """获取统计信息"""
        current_time = time.time()
        elapsed = current_time - self.start_time
        
        if self.last_update_time:
            time_since_last = current_time - self.last_update_time
        else:
            time_since_last = elapsed
            
        avg_update_rate = self.update_count / elapsed if elapsed > 0 else 0
        
        return {
            'total_updates': self.update_count,
            'elapsed_time': elapsed,
            'time_since_last_update': time_since_last,
            'avg_update_rate': avg_update_rate,
            'price_history_count': len(self.price_history)
        }


async def test_lighter_continuous_updates():
    """测试Lighter客户端的连续数据更新"""
    logger.info("🚀 开始测试Lighter客户端连续数据更新...")
    
    # 创建测试监控器
    monitor = LighterTestMonitor()
    
    # 创建Lighter客户端
    client = LighterClient(symbol="BTC/USDT", is_paper_trading=True)
    
    # 设置回调函数
    client.set_orderbook_callback(monitor.on_orderbook_update)
    
    try:
        # 初始化客户端
        logger.info("🔌 初始化Lighter客户端...")
        success = await client.initialize()
        
        if not success:
            logger.error("❌ Lighter客户端初始化失败")
            return False
        
        logger.info("✅ Lighter客户端初始化成功")
        
        # 运行测试 - 监控5分钟
        test_duration = 300  # 5分钟
        check_interval = 30   # 每30秒检查一次
        
        logger.info(f"📊 开始监控数据更新，测试时长: {test_duration}秒")
        
        for i in range(test_duration // check_interval):
            await asyncio.sleep(check_interval)
            
            # 获取统计信息
            stats = monitor.get_stats()
            client_status = client.get_status()
            
            logger.info("📈 测试进度报告",
                       progress=f"{(i+1)*check_interval}/{test_duration}s",
                       **stats,
                       connection_status=client_status.get('connection_status'),
                       connection_health=client_status.get('connection_health'),
                       reconnect_attempts=client_status.get('reconnect_attempts', 0))
            
            # 检查是否有数据更新
            if stats['time_since_last_update'] > 60:  # 超过1分钟没有更新
                logger.warning("⚠️ 数据更新异常，超过1分钟没有收到更新")
            
            # 检查更新频率
            if stats['avg_update_rate'] < 0.1:  # 平均更新频率低于0.1次/秒
                logger.warning("⚠️ 数据更新频率过低")
        
        # 最终统计
        final_stats = monitor.get_stats()
        final_status = client.get_status()
        
        logger.info("🎯 测试完成 - 最终统计",
                   **final_stats,
                   **final_status)
        
        # 判断测试是否成功
        success = (
            final_stats['total_updates'] > 0 and
            final_stats['time_since_last_update'] < 120 and  # 最后更新不超过2分钟前
            final_stats['avg_update_rate'] > 0.05  # 平均更新频率大于0.05次/秒
        )
        
        if success:
            logger.info("✅ 测试成功：Lighter客户端数据更新正常")
        else:
            logger.error("❌ 测试失败：Lighter客户端数据更新异常")
        
        return success
        
    except Exception as e:
        logger.error("❌ 测试过程中发生异常", error=str(e))
        return False
        
    finally:
        # 清理资源
        logger.info("🧹 清理测试资源...")
        await client.close()
        logger.info("✅ 测试资源清理完成")


async def main():
    """主函数"""
    try:
        logger.info("🔧 Lighter客户端连续数据更新测试")
        logger.info("=" * 50)
        
        success = await test_lighter_continuous_updates()
        
        logger.info("=" * 50)
        if success:
            logger.info("🎉 所有测试通过！Lighter客户端修复成功")
            sys.exit(0)
        else:
            logger.error("💥 测试失败！需要进一步检查")
            sys.exit(1)
            
    except KeyboardInterrupt:
        logger.info("⏹️ 测试被用户中断")
        sys.exit(0)
    except Exception as e:
        logger.error("💥 测试运行失败", error=str(e))
        sys.exit(1)


if __name__ == "__main__":
    # 检查Python版本
    if sys.version_info < (3, 8):
        print("错误: 需要Python 3.8或更高版本")
        sys.exit(1)
    
    # 运行测试
    asyncio.run(main())
