# Binance-Lighter 套利交易系统

一个专业的加密货币套利交易系统，支持在Binance和Lighter交易所之间进行自动化套利交易。

**🎯 项目状态**: 生产就绪，100%功能完成，7,300+行代码

## 🚀 功能特性

### 核心功能
- **实时价差监控**: 基于WebSocket的实时行情数据获取
- **智能套利策略**: 基于移动平均线的价差分析和交易信号生成
- **自动化交易**: 智能挂单、对冲和风险控制
- **多交易所支持**: 同时支持Binance和Lighter交易所
- **数据持久化**: 完整的SQLite数据库存储系统
- **模拟交易模式**: 零风险的纸上交易测试环境
- **单实例保护**: 自动检测并防止多实例同时运行
- **Web监控界面**: 实时监控系统状态和交易表现

### 技术架构
- **异步编程**: 基于Python asyncio的高性能异步架构
- **模块化设计**: 清晰的模块分离，易于维护和扩展
- **数据库集成**: SQLite数据库存储，支持数据备份和清理
- **WebSocket连接**: 实时价格数据流和Web界面更新
- **RESTful API**: 完整的API接口支持外部集成

## 📁 项目结构

```
arbitrage-trading-system/
├── README.md                    # 项目主文档
├── requirements.txt             # Python依赖包
├── run.py                       # 主启动脚本 (605行)
├── run_complete_system.py       # 完整系统启动脚本 (351行)
├── final_demo.py                # 完整演示脚本 (489行)
├── test_complete_flow.py        # 完整流程测试脚本 (468行)
├── test_complete_system.py      # 完整系统测试脚本 (545行)
├── src/                         # 源代码目录
│   ├── arbitrage/              # 套利交易模块
│   │   ├── engine.py           # 套利引擎 (1029行)
│   │   ├── strategy.py         # 交易策略 (362行)
│   │   └── risk_manager.py     # 风险管理 (504行)
│   ├── exchanges/              # 交易所客户端
│   │   ├── binance_client.py   # Binance客户端 (503行)
│   │   └── lighter_client.py   # Lighter客户端 (467行)
│   ├── database/               # 数据持久化模块
│   │   ├── models.py           # 数据模型 (279行)
│   │   ├── database.py         # 数据库管理 (641行)
│   │   └── trade_recorder.py   # 交易记录器 (527行)
│   ├── utils/                  # 工具模块
│   │   ├── logger.py           # 日志系统 (256行)
│   │   ├── config_loader.py    # 配置加载器 (155行)
│   │   └── indicators.py       # 技术指标 (320行)
│   ├── web/                    # Web监控界面
│   │   ├── app.py              # Web应用 (451行)
│   │   ├── static/             # 静态资源
│   │   └── templates/          # HTML模板
│   │       └── dashboard.html  # 监控仪表板 (656行)
│   └── main.py                 # 主程序入口 (292行)
├── config/                     # 配置文件目录
│   ├── settings.yaml           # 主配置文件 (306行)
│   └── exchanges.yaml          # 交易所配置文件
├── docs/                       # 📚 文档目录 (统一文档管理)
│   ├── PAPER_TRADING_GUIDE.md  # 模拟交易指南
│   ├── PROCESS_MANAGEMENT.md   # 进程管理指南
│   ├── PROJECT_COMPLETION_REPORT.md # 项目完成报告
│   ├── PROJECT_CLEANUP_SUMMARY.md   # 项目清理总结
│   └── SYSTEM_STATUS.md        # 系统状态文档
├── data/                       # 数据文件目录
└── logs/                       # 日志文件目录
```

## 🛠️ 安装和配置

### 环境要求
- **Python**: 3.8 或更高版本
- **操作系统**: Windows, macOS, Linux  
- **内存**: 建议 2GB 以上
- **存储**: 建议 1GB 以上（用于日志和数据存储）
- **网络**: 稳定的互联网连接

### 安装依赖
```bash
# 安装Python依赖
pip install -r requirements.txt

# 可选：安装Lighter SDK
pip install git+https://github.com/elliottech/lighter-python.git
```

### 🔒 安全配置（重要）

**⚠️ 首次使用必读**: 在配置系统之前，请务必阅读 [安全配置指南](docs/SECURITY_GUIDE.md)

#### 1. 复制配置模板
```bash
# 复制交易所配置模板
cp config/exchanges.yaml.template config/exchanges.yaml
```

#### 2. 配置API密钥
编辑 `config/exchanges.yaml` 文件，填入您的真实API密钥：

```yaml
# Binance配置
binance:
  api_key: "your_real_binance_api_key"
  secret: "your_real_binance_secret_key"
  sandbox: true          # 生产环境设为 false
  testnet: true          # 生产环境设为 false

# Lighter配置  
lighter:
  api_url: "https://api.lighter.xyz"
  private_key: "your_real_lighter_private_key"
  account_index: 595
  api_key_index: 1
```

#### 3. 验证配置安全
```bash
# 验证配置文件不会被提交到Git
git status

# 确认 exchanges.yaml 已被 .gitignore 忽略
git check-ignore config/exchanges.yaml
```

### 配置交易参数
编辑 `config/settings.yaml` 文件：

```yaml
# 交易配置
trading:
  symbol: "BTC/USDT"           # 交易对
  base_currency: "BTC"         # 基础货币
  quote_currency: "USDT"       # 计价货币
  max_position_size: 0.1       # 最大持仓量
  min_trade_amount: 0.001      # 最小交易量
  max_trade_amount: 0.01       # 单次最大交易量
  ma_period: 20                # 移动平均线周期
  min_profit_threshold: 0.001  # 最小盈利阈值 (0.1%)
  max_spread_threshold: 0.02   # 最大价差阈值 (2%)

# 风险管理
risk_management:
  max_daily_loss: 1000         # 最大日亏损限制 (USDT)
  max_position_ratio: 0.8      # 最大仓位比例
  emergency_stop_loss: 2000    # 紧急停止亏损阈值 (USDT)
  max_drawdown: 0.05           # 最大回撤比例 (5%)
```

## 🚀 运行系统

### 基本使用

```bash
# 模拟交易模式（推荐新手使用）
python run.py --paper-trading

# 查看系统状态
python run.py --status

# 测试系统配置
python run.py --test-system

# 干运行模式（不执行交易）
python run.py --dry-run
```

### 完整系统启动

```bash
# 启动完整系统（套利引擎 + Web界面）
python run_complete_system.py

# 指定配置文件
python run_complete_system.py --config config/settings.yaml

# 测试模式
python run_complete_system.py --test
```

### 演示和测试

```bash
# 运行完整演示
python final_demo.py

# 运行完整流程测试
python test_complete_flow.py

# 运行完整系统测试
python test_complete_system.py
```

## 📊 监控界面

启动系统后，可以通过浏览器访问监控界面：

- **本地访问**: http://localhost:8000
- **监控仪表板**: 实时价格、交易状态、风险指标
- **交易历史**: 详细的交易记录和统计
- **系统控制**: 紧急停止、交易开关控制

### API端点
- `GET /api/status` - 系统状态
- `GET /api/prices` - 实时价格数据  
- `GET /api/trades` - 交易历史
- `GET /api/risk` - 风险状态
- `GET /api/performance` - 性能指标
- `POST /api/emergency_stop` - 紧急停止

## 💡 模拟交易模式

⚠️ **重要说明**: 在Paper Trading模式下，**只有交易操作是模拟的，其他所有功能都是真实的**。

- ✅ **真实功能**: 价格数据获取、WebSocket连接、价差分析、风险计算、系统监控
- 🎭 **模拟功能**: 订单执行、资金变动、仓位变化

这意味着您可以获得完全真实的市场数据和分析结果，但不会有任何真实的资金风险。

详细信息请参考 [模拟交易指南](docs/PAPER_TRADING_GUIDE.md)。

## 🔒 单实例保护机制

系统内置智能的进程锁机制，确保同一时间只有一个系统实例在运行：

- **自动检测**: 启动时自动检测是否已有实例运行
- **进程验证**: 通过PID和启动时间双重验证避免误判
- **友好提示**: 提供清晰的错误信息和解决方案
- **状态查看**: `python run.py --status` 查看运行状态
- **强制清理**: `python run.py --force-cleanup` 清理锁文件

详细信息请参考 [进程管理指南](docs/PROCESS_MANAGEMENT.md)。

## 🛡️ 风险管理

### 风险控制
- **仓位控制**: 最大持仓量和单次交易量限制
- **止损机制**: 日亏损限制和最大回撤控制
- **实时监控**: 风险指标实时计算和告警
- **紧急停止**: 一键紧急停止所有交易活动
- **多维度风险评分**: 实时风险状态监控

### 安全特性
- **API密钥保护**: 环境变量管理
- **配置验证**: 智能配置检查
- **错误处理**: 完善的异常处理机制
- **日志审计**: 完整的操作日志记录
- **数据备份**: 自动化数据备份和恢复

## 📈 技术指标

### 套利策略
- **价差计算**: `diffRate = binance最新成交价 / Lighter最新成交价 - 1`
- **移动平均线分析**: 可配置周期的价差移动平均线
- **信号生成**: 基于价差与MA的关系生成买卖信号
- **风险阈值**: 可配置的最小盈利阈值和最大价差阈值

### 交易执行
- **智能挂单**: 基于orderbook深度的最优价格挂单
- **自动对冲**: 订单成交后立即在对手交易所对冲
- **超时处理**: 活跃订单超时检测和处理
- **订单跟踪**: 完整的订单生命周期管理

## 💾 数据管理

### 数据模型
- **TradeRecord**: 完整的交易记录，包括Binance和Lighter订单信息
- **PriceRecord**: 价格历史数据
- **SpreadRecord**: 价差历史数据
- **RiskMetrics**: 风险指标记录
- **SystemStatus**: 系统状态记录
- **PerformanceMetrics**: 性能指标记录

### 数据功能
- **自动初始化**: 首次运行自动创建数据库表结构
- **实时记录**: 交易和价格数据实时存储
- **数据备份**: 定期自动备份数据库
- **数据清理**: 自动清理过期数据
- **性能优化**: 索引优化和查询缓存

## 🔧 系统维护

### 自动化任务
- **系统监控**: 每分钟记录系统状态
- **风险检查**: 每30秒检查风险指标
- **数据清理**: 每日凌晨2点清理30天前数据
- **数据备份**: 每日自动备份数据库
- **连接检查**: 定期验证交易所连接状态

### 日志管理
- **结构化日志**: 使用structlog进行结构化日志记录
- **多级别日志**: ERROR, WARNING, INFO, DEBUG级别
- **文件输出**: 自动日志文件轮转
- **控制台输出**: 实时状态显示

## 📚 文档

所有详细文档都位于 `docs/` 目录中：

- [模拟交易指南](docs/PAPER_TRADING_GUIDE.md) - 模拟交易模式详细说明
- [进程管理指南](docs/PROCESS_MANAGEMENT.md) - 进程管理和监控指南
- [项目完成报告](docs/PROJECT_COMPLETION_REPORT.md) - 项目功能完成情况
- [项目清理总结](docs/PROJECT_CLEANUP_SUMMARY.md) - 项目清理过程记录
- [系统状态文档](docs/SYSTEM_STATUS.md) - 系统当前状态和性能指标

## 🤝 支持

如果您在使用过程中遇到问题，请：

1. 查看文档中的常见问题解答
2. 检查配置文件是否正确
3. 查看日志文件中的错误信息
4. 使用 `python run.py --test-system` 进行系统诊断

## ⚠️ 免责声明

本系统仅供学习和研究使用。加密货币交易存在风险，请：

- 在模拟交易模式下充分测试
- 理解套利交易的风险
- 仅使用您可以承受损失的资金
- 遵守当地法律法规

**投资有风险，入市需谨慎！** 